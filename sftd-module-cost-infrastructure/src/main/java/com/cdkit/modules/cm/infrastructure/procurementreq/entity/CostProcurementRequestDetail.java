package com.cdkit.modules.cm.infrastructure.procurementreq.entity;

import java.io.Serializable;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.cdkitframework.poi.excel.annotation.Excel;
import java.util.Date;
import io.swagger.v3.oas.annotations.media.Schema;

import java.io.UnsupportedEncodingException;

/**
 * @Description: 采购申请明细
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
@Schema(description="cost_procurement_request_detail对象")
@Data
@TableName("cost_procurement_request_detail")
public class CostProcurementRequestDetail implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键ID*/
	@TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;
	/**采购申请ID*/
    @Schema(description = "采购申请ID")
    private String requestId;
	/**预算编码*/
	@Excel(name = "预算编码", width = 15)
    @Schema(description = "预算编码")
    private String budgetCode;
	/**预算名称*/
	@Excel(name = "预算名称", width = 15)
    @Schema(description = "预算名称")
    private String budgetName;
	/**总预算量*/
	@Excel(name = "总预算量", width = 15)
    @Schema(description = "总预算量")
    private java.math.BigDecimal totalQuantity;
	/**审批中量*/
	@Excel(name = "审批中量", width = 15)
    @Schema(description = "审批中量")
    private java.math.BigDecimal unitPrice;
	/**可采购量*/
	@Excel(name = "可采购量", width = 15)
    @Schema(description = "可采购量")
    private java.math.BigDecimal totalPrice;
	/**本次采购量*/
	@Excel(name = "本次采购量", width = 15)
    @Schema(description = "本次采购量")
    private java.math.BigDecimal purchaseQuantity;
	/**备注*/
	@Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;
	/**创建时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;
	/**创建人*/
    @Schema(description = "创建人")
    private String createBy;
	/**更新时间*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;
	/**更新人*/
    @Schema(description = "更新人")
    private String updateBy;
	/**租户id*/
	@Excel(name = "租户id", width = 15)
    @Schema(description = "租户id")
    private Integer tenantId;
	/**删除标识 0:未删除 1:删除*/
	@Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    @TableLogic
    private Integer delFlag;
	/**所属部门*/
    @Schema(description = "所属部门")
    private String sysOrgCode;
}
