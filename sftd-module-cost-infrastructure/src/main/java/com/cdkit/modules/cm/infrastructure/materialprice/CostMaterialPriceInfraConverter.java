package com.cdkit.modules.cm.infrastructure.materialprice;

import cn.hutool.core.bean.BeanUtil;
import com.cdkit.modules.cm.domain.materialprice.mode.entity.CostMaterialPriceEntity;
import com.cdkit.modules.cm.infrastructure.materialprice.entity.CostMaterialPrice;

import java.util.List;

/**
 * 材料单价基础设施转换器
 * <AUTHOR>
 * @date 2025/07/16
 */
public class CostMaterialPriceInfraConverter {

    /**
     * 领域实体转基础设施实体
     */
    public static CostMaterialPrice convert(CostMaterialPriceEntity entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostMaterialPrice.class);
    }

    /**
     * 基础设施实体转领域实体
     */
    public static CostMaterialPriceEntity convert(CostMaterialPrice entity) {
        if (entity == null) {
            return null;
        }
        return BeanUtil.copyProperties(entity, CostMaterialPriceEntity.class);
    }

    /**
     * 基础设施实体列表转领域实体列表
     */
    public static List<CostMaterialPriceEntity> convertList(List<CostMaterialPrice> entityList) {
        return BeanUtil.copyToList(entityList, CostMaterialPriceEntity.class);
    }

    /**
     * 领域实体列表转基础设施实体列表
     */
    public static List<CostMaterialPrice> convertToInfraList(List<CostMaterialPriceEntity> entityList) {
        return BeanUtil.copyToList(entityList, CostMaterialPrice.class);
    }
}
