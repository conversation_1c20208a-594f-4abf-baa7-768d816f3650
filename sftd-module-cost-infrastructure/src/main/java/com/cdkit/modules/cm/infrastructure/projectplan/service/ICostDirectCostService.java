package com.cdkit.modules.cm.infrastructure.projectplan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostDirectCost;

import java.util.List;

/**
 * @Description: 直接成本明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
public interface ICostDirectCostService extends IService<CostDirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostDirectCost>
	 */
	public List<CostDirectCost> selectByMainId(String mainId);
}
