package com.cdkit.modules.cm.infrastructure.annualbudget.service.impl;


import com.cdkit.modules.cm.infrastructure.annualbudget.entity.CostAnnualBudgetCenterIndirectCost;
import com.cdkit.modules.cm.infrastructure.annualbudget.mapper.CostAnnualBudgetCenterIndirectCostMapper;
import com.cdkit.modules.cm.infrastructure.annualbudget.service.ICostAnnualBudgetCenterIndirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 其他成本-本中心间接成本
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetCenterIndirectCostServiceImpl extends ServiceImpl<CostAnnualBudgetCenterIndirectCostMapper, CostAnnualBudgetCenterIndirectCost> implements ICostAnnualBudgetCenterIndirectCostService {
	
	@Autowired
	private CostAnnualBudgetCenterIndirectCostMapper costAnnualBudgetCenterIndirectCostMapper;
	
	@Override
	public List<CostAnnualBudgetCenterIndirectCost> selectByMainId(String mainId) {
		return costAnnualBudgetCenterIndirectCostMapper.selectByMainId(mainId);
	}
}
