package com.cdkit.modules.cm.infrastructure.projectplan.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostTaxCost;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 税金及附加明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
public interface CostTaxCostMapper extends BaseMapper<CostTaxCost> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostTaxCost>
   */
	public List<CostTaxCost> selectByMainId(@Param("mainId") String mainId);
}
