<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostTaxCostMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		UPDATE cost_tax_cost
		SET del_flag = 1
		WHERE
			 plan_id = #{mainId} AND del_flag = 0</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.cm.infrastructure.projectplan.entity.CostTaxCost">
		SELECT * 
		FROM  cost_tax_cost
		WHERE
			 plan_id = #{mainId} and del_flag=0	</select>
</mapper>
