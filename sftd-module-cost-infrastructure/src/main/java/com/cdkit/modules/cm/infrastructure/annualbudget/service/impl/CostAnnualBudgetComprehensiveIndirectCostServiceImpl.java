package com.cdkit.modules.cm.infrastructure.annualbudget.service.impl;


import com.cdkit.modules.cm.infrastructure.annualbudget.entity.CostAnnualBudgetComprehensiveIndirectCost;
import com.cdkit.modules.cm.infrastructure.annualbudget.mapper.CostAnnualBudgetComprehensiveIndirectCostMapper;
import com.cdkit.modules.cm.infrastructure.annualbudget.service.ICostAnnualBudgetComprehensiveIndirectCostService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 其他成本-综合管理间接成本
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetComprehensiveIndirectCostServiceImpl extends ServiceImpl<CostAnnualBudgetComprehensiveIndirectCostMapper, CostAnnualBudgetComprehensiveIndirectCost> implements ICostAnnualBudgetComprehensiveIndirectCostService {
	
	@Autowired
	private CostAnnualBudgetComprehensiveIndirectCostMapper costAnnualBudgetComprehensiveIndirectCostMapper;
	
	@Override
	public List<CostAnnualBudgetComprehensiveIndirectCost> selectByMainId(String mainId) {
		return costAnnualBudgetComprehensiveIndirectCostMapper.selectByMainId(mainId);
	}
}
