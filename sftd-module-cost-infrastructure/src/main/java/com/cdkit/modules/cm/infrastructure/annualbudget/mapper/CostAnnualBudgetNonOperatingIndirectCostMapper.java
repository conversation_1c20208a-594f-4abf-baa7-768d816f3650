package com.cdkit.modules.cm.infrastructure.annualbudget.mapper;

import java.util.List;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdkit.modules.cm.infrastructure.annualbudget.entity.CostAnnualBudgetNonOperatingIndirectCost;
import org.apache.ibatis.annotations.Param;

/**
 * @Description: 其他成本-非经营中心间接成本
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface CostAnnualBudgetNonOperatingIndirectCostMapper extends BaseMapper<CostAnnualBudgetNonOperatingIndirectCost> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<CostAnnualBudgetNonOperatingIndirectCost>
   */
	public List<CostAnnualBudgetNonOperatingIndirectCost> selectByMainId(@Param("mainId") String mainId);
}
