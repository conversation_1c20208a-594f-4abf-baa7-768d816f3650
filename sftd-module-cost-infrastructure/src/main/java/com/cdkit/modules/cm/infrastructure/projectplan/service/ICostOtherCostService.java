package com.cdkit.modules.cm.infrastructure.projectplan.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostOtherCost;

import java.util.List;

/**
 * @Description: 其他成本明细
 * @Author: cdkit-boot
 * @Date:   2025-07-18
 * @Version: V1.0
 */
public interface ICostOtherCostService extends IService<CostOtherCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostOtherCost>
	 */
	public List<CostOtherCost> selectByMainId(String mainId);
}
