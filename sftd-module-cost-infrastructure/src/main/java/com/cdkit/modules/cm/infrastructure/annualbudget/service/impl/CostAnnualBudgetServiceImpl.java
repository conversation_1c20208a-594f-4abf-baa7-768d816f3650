package com.cdkit.modules.cm.infrastructure.annualbudget.service.impl;


import com.cdkit.modules.cm.infrastructure.annualbudget.entity.*;
import com.cdkit.modules.cm.infrastructure.annualbudget.mapper.*;
import com.cdkit.modules.cm.infrastructure.annualbudget.service.ICostAnnualBudgetService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import java.io.Serializable;
import java.util.List;
import java.util.Collection;

/**
 * @Description: 年度总预算
 * @Author: sunhzh
 * @Date:   2025-07-30
 * @Version: V1.0
 */
@Service
public class CostAnnualBudgetServiceImpl extends ServiceImpl<CostAnnualBudgetMapper, CostAnnualBudget> implements ICostAnnualBudgetService {

	@Autowired
	private CostAnnualBudgetMapper costAnnualBudgetMapper;
	@Autowired
	private CostAnnualBudgetDetailMapper costAnnualBudgetDetailMapper;
	@Autowired
	private CostAnnualBudgetCenterIndirectCostMapper costAnnualBudgetCenterIndirectCostMapper;
	@Autowired
	private CostAnnualBudgetNonOperatingIndirectCostMapper costAnnualBudgetNonOperatingIndirectCostMapper;
	@Autowired
	private CostAnnualBudgetComprehensiveIndirectCostMapper costAnnualBudgetComprehensiveIndirectCostMapper;
	
	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(CostAnnualBudget costAnnualBudget, List<CostAnnualBudgetDetail> costAnnualBudgetDetailList, List<CostAnnualBudgetCenterIndirectCost> costAnnualBudgetCenterIndirectCostList, List<CostAnnualBudgetNonOperatingIndirectCost> costAnnualBudgetNonOperatingIndirectCostList, List<CostAnnualBudgetComprehensiveIndirectCost> costAnnualBudgetComprehensiveIndirectCostList) {
		costAnnualBudgetMapper.insert(costAnnualBudget);
		if(costAnnualBudgetDetailList!=null && costAnnualBudgetDetailList.size()>0) {
			for(CostAnnualBudgetDetail entity:costAnnualBudgetDetailList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetDetailMapper.insert(entity);
			}
		}
		if(costAnnualBudgetCenterIndirectCostList!=null && costAnnualBudgetCenterIndirectCostList.size()>0) {
			for(CostAnnualBudgetCenterIndirectCost entity:costAnnualBudgetCenterIndirectCostList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetCenterIndirectCostMapper.insert(entity);
			}
		}
		if(costAnnualBudgetNonOperatingIndirectCostList!=null && costAnnualBudgetNonOperatingIndirectCostList.size()>0) {
			for(CostAnnualBudgetNonOperatingIndirectCost entity:costAnnualBudgetNonOperatingIndirectCostList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetNonOperatingIndirectCostMapper.insert(entity);
			}
		}
		if(costAnnualBudgetComprehensiveIndirectCostList!=null && costAnnualBudgetComprehensiveIndirectCostList.size()>0) {
			for(CostAnnualBudgetComprehensiveIndirectCost entity:costAnnualBudgetComprehensiveIndirectCostList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetComprehensiveIndirectCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(CostAnnualBudget costAnnualBudget,List<CostAnnualBudgetDetail> costAnnualBudgetDetailList,List<CostAnnualBudgetCenterIndirectCost> costAnnualBudgetCenterIndirectCostList,List<CostAnnualBudgetNonOperatingIndirectCost> costAnnualBudgetNonOperatingIndirectCostList,List<CostAnnualBudgetComprehensiveIndirectCost> costAnnualBudgetComprehensiveIndirectCostList) {
		costAnnualBudgetMapper.updateById(costAnnualBudget);
		
		//1.先删除子表数据
		costAnnualBudgetDetailMapper.deleteByMainId(costAnnualBudget.getId());
		costAnnualBudgetCenterIndirectCostMapper.deleteByMainId(costAnnualBudget.getId());
		costAnnualBudgetNonOperatingIndirectCostMapper.deleteByMainId(costAnnualBudget.getId());
		costAnnualBudgetComprehensiveIndirectCostMapper.deleteByMainId(costAnnualBudget.getId());
		
		//2.子表数据重新插入
		if(costAnnualBudgetDetailList!=null && costAnnualBudgetDetailList.size()>0) {
			for(CostAnnualBudgetDetail entity:costAnnualBudgetDetailList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetDetailMapper.insert(entity);
			}
		}
		if(costAnnualBudgetCenterIndirectCostList!=null && costAnnualBudgetCenterIndirectCostList.size()>0) {
			for(CostAnnualBudgetCenterIndirectCost entity:costAnnualBudgetCenterIndirectCostList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetCenterIndirectCostMapper.insert(entity);
			}
		}
		if(costAnnualBudgetNonOperatingIndirectCostList!=null && costAnnualBudgetNonOperatingIndirectCostList.size()>0) {
			for(CostAnnualBudgetNonOperatingIndirectCost entity:costAnnualBudgetNonOperatingIndirectCostList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetNonOperatingIndirectCostMapper.insert(entity);
			}
		}
		if(costAnnualBudgetComprehensiveIndirectCostList!=null && costAnnualBudgetComprehensiveIndirectCostList.size()>0) {
			for(CostAnnualBudgetComprehensiveIndirectCost entity:costAnnualBudgetComprehensiveIndirectCostList) {
				//外键设置
				entity.setBudgetId(costAnnualBudget.getId());
				costAnnualBudgetComprehensiveIndirectCostMapper.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		costAnnualBudgetDetailMapper.deleteByMainId(id);
		costAnnualBudgetCenterIndirectCostMapper.deleteByMainId(id);
		costAnnualBudgetNonOperatingIndirectCostMapper.deleteByMainId(id);
		costAnnualBudgetComprehensiveIndirectCostMapper.deleteByMainId(id);
		costAnnualBudgetMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			costAnnualBudgetDetailMapper.deleteByMainId(id.toString());
			costAnnualBudgetCenterIndirectCostMapper.deleteByMainId(id.toString());
			costAnnualBudgetNonOperatingIndirectCostMapper.deleteByMainId(id.toString());
			costAnnualBudgetComprehensiveIndirectCostMapper.deleteByMainId(id.toString());
			costAnnualBudgetMapper.deleteById(id);
		}
	}
	
}
