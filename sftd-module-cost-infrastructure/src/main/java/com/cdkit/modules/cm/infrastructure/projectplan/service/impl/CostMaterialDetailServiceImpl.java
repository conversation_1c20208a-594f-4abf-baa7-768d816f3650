package com.cdkit.modules.cm.infrastructure.projectplan.service.impl;


import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostMaterialDetail;
import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostMaterialDetailMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.service.ICostMaterialDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 原料明细
 * @Author: cdkit-boot
 * @Date:   2025-07-22
 * @Version: V1.0
 */
@Service
public class CostMaterialDetailServiceImpl extends ServiceImpl<CostMaterialDetailMapper, CostMaterialDetail> implements ICostMaterialDetailService {
	
	@Autowired
	private CostMaterialDetailMapper costMaterialDetailMapper;
	
	@Override
	public List<CostMaterialDetail> selectByMainId(String mainId) {
		return costMaterialDetailMapper.selectByMainId(mainId);
	}
}
