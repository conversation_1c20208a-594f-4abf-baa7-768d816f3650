package com.cdkit.modules.cm.infrastructure.projectplan.repository;

import cn.hutool.core.map.MapUtil;
import com.cdkit.modules.cm.domain.projectplan.repository.CostProjectPlanRepository;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostProjectPlanEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostProjectPlanDetailEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostDirectCostEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostOtherCostEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostTaxCostEntity;
import com.cdkit.modules.cm.domain.projectplan.mode.entity.CostMaterialDetailEntity;

import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostProjectPlan;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostProjectPlanDetail;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostDirectCost;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostOtherCost;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostTaxCost;
import com.cdkit.modules.cm.infrastructure.projectplan.entity.CostMaterialDetail;

import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostProjectPlanMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostProjectPlanDetailMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostDirectCostMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostOtherCostMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostTaxCostMapper;
import com.cdkit.modules.cm.infrastructure.projectplan.mapper.CostMaterialDetailMapper;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.cdkit.modules.didgen.DidUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目计划仓储实现
 * 
 * <AUTHOR>
 * @date 2025/07/18
 */
@Repository
public class CostProjectPlanRepositoryImpl implements CostProjectPlanRepository {

    @Autowired
    private CostProjectPlanMapper costProjectPlanMapper;

    @Autowired
    private CostProjectPlanDetailMapper costProjectPlanDetailMapper;
    
    @Autowired
    private CostDirectCostMapper costDirectCostMapper;
    
    @Autowired
    private CostOtherCostMapper costOtherCostMapper;
    
    @Autowired
    private CostTaxCostMapper costTaxCostMapper;

    @Autowired
    private CostMaterialDetailMapper costMaterialDetailMapper;

    @Override
    public String addDomain(CostProjectPlanEntity projectPlan) {
        CostProjectPlan entity = convertToInfraEntity(projectPlan);
        costProjectPlanMapper.insert(entity);
        return entity.getId();
    }

    @Override
    public void deleteDomainById(String id) {
        deleteMain(id);
    }

    @Override
    public void deleteDomainById(List<String> ids) {
        deleteBatchMain(ids);
    }

    @Override
    public CostProjectPlanEntity updateDomainById(CostProjectPlanEntity projectPlan) {
        CostProjectPlan entity = convertToInfraEntity(projectPlan);
        costProjectPlanMapper.updateById(entity);
        return convertToDomainEntity(entity);
    }

    @Override
    public CostProjectPlanEntity getDomainById(String id) {
        CostProjectPlan entity = costProjectPlanMapper.selectById(id);
        return entity != null ? convertToDomainEntity(entity) : null;
    }

    @Override
    public PageRes<CostProjectPlanEntity> page(CostProjectPlanEntity queryEntity, PageReq pageReq) {
        // 构建查询条件
        LambdaQueryWrapper<CostProjectPlan> queryWrapper = buildQueryWrapper(queryEntity);

        // 创建分页对象
        com.baomidou.mybatisplus.extension.plugins.pagination.Page<CostProjectPlan> page =
            new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(pageReq.getCurrent(), pageReq.getSize());

        // 执行分页查询
        com.baomidou.mybatisplus.core.metadata.IPage<CostProjectPlan> pageResult =
            costProjectPlanMapper.selectPage(page, queryWrapper);

        // 转换结果
        List<CostProjectPlanEntity> entityList = pageResult.getRecords().stream()
            .map(this::convertToDomainEntity)
            .collect(Collectors.toList());

        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), entityList,
            pageResult.getTotal(), pageResult.getPages());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<CostProjectPlan> buildQueryWrapper(CostProjectPlanEntity queryEntity) {
        LambdaQueryWrapper<CostProjectPlan> queryWrapper = new LambdaQueryWrapper<>();

        if (queryEntity != null) {
            // 计划名称模糊查询
            if (StringUtils.hasText(queryEntity.getPlanName())) {
                queryWrapper.like(CostProjectPlan::getPlanName, queryEntity.getPlanName());
            }

            // 项目名称模糊查询
            if (StringUtils.hasText(queryEntity.getProjectName())) {
                queryWrapper.like(CostProjectPlan::getProjectName, queryEntity.getProjectName());
            }

            // 项目编号模糊查询
            if (StringUtils.hasText(queryEntity.getProjectCode())) {
                queryWrapper.like(CostProjectPlan::getProjectCode, queryEntity.getProjectCode());
            }

            // 计划编号精确查询
            if (StringUtils.hasText(queryEntity.getPlanCode())) {
                queryWrapper.eq(CostProjectPlan::getPlanCode, queryEntity.getPlanCode());
            }

            // 状态精确查询
            if (StringUtils.hasText(queryEntity.getProjectPlanStatus())) {
                queryWrapper.eq(CostProjectPlan::getProjectPlanStatus, queryEntity.getProjectPlanStatus());
            }

            // 计划类型精确查询
            if (StringUtils.hasText(queryEntity.getPlanType())) {
                queryWrapper.eq(CostProjectPlan::getPlanType, queryEntity.getPlanType());
            }

            // 中心精确查询
            if (StringUtils.hasText(queryEntity.getCenter())) {
                queryWrapper.eq(CostProjectPlan::getCenter, queryEntity.getCenter());
            }

            // 项目组精确查询
            if (StringUtils.hasText(queryEntity.getProjectGroup())) {
                queryWrapper.eq(CostProjectPlan::getProjectGroup, queryEntity.getProjectGroup());
            }
        }

        // 只查询年度计划（作为主数据）
        queryWrapper.eq(CostProjectPlan::getPlanType, "ANNUAL");

        // 默认按创建时间倒序排列
        queryWrapper.orderByDesc(CostProjectPlan::getCreateTime);

        return queryWrapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMain(CostProjectPlanEntity costProjectPlan,
                           List<CostProjectPlanDetailEntity> detailList,
                           List<CostDirectCostEntity> directCostList,
                           List<CostOtherCostEntity> otherCostList,
                           List<CostTaxCostEntity> taxCostList,
                           List<CostMaterialDetailEntity> materialDetailList) {
        
        // 保存主表
        CostProjectPlan mainEntity = convertToInfraEntity(costProjectPlan);

        mainEntity.setPlanCode(DidUtil.getDid("JH_CODE", MapUtil.empty()));
        costProjectPlanMapper.insert(mainEntity);
        
        String planId = mainEntity.getId();
        
        // 保存明细表
        if (detailList != null && !detailList.isEmpty()) {
            for (CostProjectPlanDetailEntity detail : detailList) {
                CostProjectPlanDetail detailEntity = convertToInfraDetailEntity(detail);
                detailEntity.setPlanId(planId);
                costProjectPlanDetailMapper.insert(detailEntity);
            }
        }
        
        // 保存直接成本
        if (directCostList != null && !directCostList.isEmpty()) {
            for (CostDirectCostEntity directCost : directCostList) {
                CostDirectCost costEntity = convertToInfraDirectCostEntity(directCost);
                costEntity.setPlanId(planId);
                costDirectCostMapper.insert(costEntity);
            }
        }
        
        // 保存其他成本
        if (otherCostList != null && !otherCostList.isEmpty()) {
            for (CostOtherCostEntity otherCost : otherCostList) {
                CostOtherCost costEntity = convertToInfraOtherCostEntity(otherCost);
                costEntity.setPlanId(planId);
                costOtherCostMapper.insert(costEntity);
            }
        }
        
        // 保存税金及附加
        if (taxCostList != null && !taxCostList.isEmpty()) {
            for (CostTaxCostEntity taxCost : taxCostList) {
                CostTaxCost costEntity = convertToInfraTaxCostEntity(taxCost);
                costEntity.setPlanId(planId);
                costTaxCostMapper.insert(costEntity);
            }
        }

        // 保存原料明细
        if (materialDetailList != null && !materialDetailList.isEmpty()) {
            for (CostMaterialDetailEntity materialDetail : materialDetailList) {
                CostMaterialDetail detailEntity = convertToInfraMaterialDetailEntity(materialDetail);
                detailEntity.setPlanId(planId);
                costMaterialDetailMapper.insert(detailEntity);
            }
        }

        // 更新主表ID
        costProjectPlan.setId(planId);

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateMain(CostProjectPlanEntity costProjectPlan,
                             List<CostProjectPlanDetailEntity> detailList,
                             List<CostDirectCostEntity> directCostList,
                             List<CostOtherCostEntity> otherCostList,
                             List<CostTaxCostEntity> taxCostList,
                             List<CostMaterialDetailEntity> materialDetailList) {
        
        String planId = costProjectPlan.getId();
        
        // 更新主表
        CostProjectPlan mainEntity = convertToInfraEntity(costProjectPlan);
        costProjectPlanMapper.updateById(mainEntity);
        
        // 删除原有子表数据
        deleteSubTables(planId);
        
        // 重新插入子表数据
        return saveSubTables(planId, detailList, directCostList, otherCostList, taxCostList, materialDetailList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteMain(String id) {
        // 删除子表数据
        deleteSubTables(id);
        
        // 删除主表数据
        costProjectPlanMapper.deleteById(id);
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteBatchMain(List<String> ids) {
        for (String id : ids) {
            deleteMain(id);
        }
        return true;
    }

    @Override
    public List<CostProjectPlanDetailEntity> queryDetailByPlanId(String planId) {
        List<CostProjectPlanDetail> detailList = costProjectPlanDetailMapper.selectByMainId(planId);
        return detailList.stream()
            .map(this::convertToDomainDetailEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<CostDirectCostEntity> queryDirectCostByPlanId(String planId) {
        List<CostDirectCost> directCostList = costDirectCostMapper.selectByMainId(planId);
        return directCostList.stream()
            .map(this::convertToDomainDirectCostEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<CostOtherCostEntity> queryOtherCostByPlanId(String planId) {
        List<CostOtherCost> otherCostList = costOtherCostMapper.selectByMainId(planId);
        return otherCostList.stream()
            .map(this::convertToDomainOtherCostEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<CostTaxCostEntity> queryTaxCostByPlanId(String planId) {
        List<CostTaxCost> taxCostList = costTaxCostMapper.selectByMainId(planId);
        return taxCostList.stream()
            .map(this::convertToDomainTaxCostEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<CostMaterialDetailEntity> queryMaterialDetailByPlanId(String planId) {
        List<CostMaterialDetail> materialDetailList = costMaterialDetailMapper.selectByMainId(planId);
        return materialDetailList.stream()
            .map(this::convertToDomainMaterialDetailEntity)
            .collect(Collectors.toList());
    }

    @Override
    public CostProjectPlanEntity queryByIdWithDetails(String id) {
        CostProjectPlan mainEntity = costProjectPlanMapper.selectById(id);
        if (mainEntity == null) {
            return null;
        }
        
        CostProjectPlanEntity domainEntity = convertToDomainEntity(mainEntity);
        
        // 查询子表数据
        domainEntity.setCostProjectPlanDetailList(queryDetailByPlanId(id));
        domainEntity.setCostDirectCostList(queryDirectCostByPlanId(id));
        domainEntity.setCostOtherCostList(queryOtherCostByPlanId(id));
        domainEntity.setCostTaxCostList(queryTaxCostByPlanId(id));
        domainEntity.setCostMaterialDetailList(queryMaterialDetailByPlanId(id));
        
        return domainEntity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CostProjectPlanEntity calculateBudgetBasis(String planId) {
        CostProjectPlanEntity projectPlan = queryByIdWithDetails(planId);
        if (projectPlan == null) {
            throw new IllegalArgumentException("项目计划不存在，ID: " + planId);
        }
        
        // 执行计算
        projectPlan.calculateBudgetBasis();
        
        // 更新数据
        updateMain(projectPlan,
                  projectPlan.getCostProjectPlanDetailList(),
                  projectPlan.getCostDirectCostList(),
                  projectPlan.getCostOtherCostList(),
                  projectPlan.getCostTaxCostList(),
                  projectPlan.getCostMaterialDetailList());
        
        return projectPlan;
    }

    @Override
    public List<CostProjectPlanEntity> queryByStatus(String status) {
        LambdaQueryWrapper<CostProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProjectPlan::getProjectPlanStatus, status);
        
        List<CostProjectPlan> entityList = costProjectPlanMapper.selectList(queryWrapper);
        return entityList.stream()
            .map(this::convertToDomainEntity)
            .collect(Collectors.toList());
    }

    @Override
    public List<CostProjectPlanEntity> queryByProjectCode(String projectCode) {
        LambdaQueryWrapper<CostProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProjectPlan::getProjectCode, projectCode);
        
        List<CostProjectPlan> entityList = costProjectPlanMapper.selectList(queryWrapper);
        return entityList.stream()
            .map(this::convertToDomainEntity)
            .collect(Collectors.toList());
    }

    /**
     * 删除子表数据 - 使用逻辑删除
     */
    private void deleteSubTables(String planId) {
        // 删除明细数据 - 逻辑删除
        LambdaQueryWrapper<CostProjectPlanDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProjectPlanDetail::getPlanId, planId);
        costProjectPlanDetailMapper.delete(queryWrapper);

        // 删除直接成本数据 - 逻辑删除
        LambdaQueryWrapper<CostDirectCost> queryDirectCostWrapper = new LambdaQueryWrapper<>();
        queryDirectCostWrapper.eq(CostDirectCost::getPlanId, planId);
        costDirectCostMapper.delete(queryDirectCostWrapper);

        // 删除其他成本数据 - 逻辑删除
        LambdaQueryWrapper<CostOtherCost> queryCostOtherCostWrapper = new LambdaQueryWrapper<>();
        queryCostOtherCostWrapper.eq(CostOtherCost::getPlanId, planId);
        costOtherCostMapper.delete(queryCostOtherCostWrapper);

        // 删除税金及附加数据 - 逻辑删除
        LambdaQueryWrapper<CostTaxCost> queryCostTaxCostWrapper = new LambdaQueryWrapper<>();
        queryCostTaxCostWrapper.eq(CostTaxCost::getPlanId, planId);
        costTaxCostMapper.delete(queryCostTaxCostWrapper);

        // 删除原料明细数据 - 逻辑删除
        LambdaQueryWrapper<CostMaterialDetail> queryCostMaterialDetailWrapper = new LambdaQueryWrapper<>();
        queryCostMaterialDetailWrapper.eq(CostMaterialDetail::getPlanId, planId);
        costMaterialDetailMapper.delete(queryCostMaterialDetailWrapper);
    }

    /**
     * 保存子表数据
     */
    private boolean saveSubTables(String planId,
                                 List<CostProjectPlanDetailEntity> detailList,
                                 List<CostDirectCostEntity> directCostList,
                                 List<CostOtherCostEntity> otherCostList,
                                 List<CostTaxCostEntity> taxCostList,
                                 List<CostMaterialDetailEntity> materialDetailList) {
        
        // 保存明细表
        if (detailList != null && !detailList.isEmpty()) {
            for (CostProjectPlanDetailEntity detail : detailList) {
                CostProjectPlanDetail detailEntity = convertToInfraDetailEntity(detail);
                detailEntity.setPlanId(planId);
                detailEntity.setId(null);
                costProjectPlanDetailMapper.insert(detailEntity);
            }
        }
        
        // 保存直接成本
        if (directCostList != null && !directCostList.isEmpty()) {
            for (CostDirectCostEntity directCost : directCostList) {
                CostDirectCost costEntity = convertToInfraDirectCostEntity(directCost);
                costEntity.setPlanId(planId);
                costEntity.setId(null);
                costDirectCostMapper.insert(costEntity);
            }
        }
        
        // 保存其他成本
        if (otherCostList != null && !otherCostList.isEmpty()) {
            for (CostOtherCostEntity otherCost : otherCostList) {
                CostOtherCost costEntity = convertToInfraOtherCostEntity(otherCost);
                costEntity.setPlanId(planId);
                costEntity.setId(null);
                costOtherCostMapper.insert(costEntity);
            }
        }
        
        // 保存税金及附加
        if (taxCostList != null && !taxCostList.isEmpty()) {
            for (CostTaxCostEntity taxCost : taxCostList) {
                CostTaxCost costEntity = convertToInfraTaxCostEntity(taxCost);
                costEntity.setPlanId(planId);
                costEntity.setId(null);
                costTaxCostMapper.insert(costEntity);
            }
        }

        // 保存原料明细
        if (materialDetailList != null && !materialDetailList.isEmpty()) {
            for (CostMaterialDetailEntity materialDetail : materialDetailList) {
                CostMaterialDetail detailEntity = convertToInfraMaterialDetailEntity(materialDetail);
                detailEntity.setPlanId(planId);
                detailEntity.setId(null);
                costMaterialDetailMapper.insert(detailEntity);
            }
        }

        return true;
    }

    // 转换方法
    private CostProjectPlan convertToInfraEntity(CostProjectPlanEntity domainEntity) {
        CostProjectPlan infraEntity = new CostProjectPlan();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private CostProjectPlanEntity convertToDomainEntity(CostProjectPlan infraEntity) {
        CostProjectPlanEntity domainEntity = new CostProjectPlanEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }

    private CostProjectPlanDetail convertToInfraDetailEntity(CostProjectPlanDetailEntity domainEntity) {
        CostProjectPlanDetail infraEntity = new CostProjectPlanDetail();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private CostProjectPlanDetailEntity convertToDomainDetailEntity(CostProjectPlanDetail infraEntity) {
        CostProjectPlanDetailEntity domainEntity = new CostProjectPlanDetailEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }

    private CostDirectCost convertToInfraDirectCostEntity(CostDirectCostEntity domainEntity) {
        CostDirectCost infraEntity = new CostDirectCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private CostDirectCostEntity convertToDomainDirectCostEntity(CostDirectCost infraEntity) {
        CostDirectCostEntity domainEntity = new CostDirectCostEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }

    private CostOtherCost convertToInfraOtherCostEntity(CostOtherCostEntity domainEntity) {
        CostOtherCost infraEntity = new CostOtherCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private CostOtherCostEntity convertToDomainOtherCostEntity(CostOtherCost infraEntity) {
        CostOtherCostEntity domainEntity = new CostOtherCostEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }

    private CostTaxCost convertToInfraTaxCostEntity(CostTaxCostEntity domainEntity) {
        CostTaxCost infraEntity = new CostTaxCost();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private CostTaxCostEntity convertToDomainTaxCostEntity(CostTaxCost infraEntity) {
        CostTaxCostEntity domainEntity = new CostTaxCostEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }

    private CostMaterialDetail convertToInfraMaterialDetailEntity(CostMaterialDetailEntity domainEntity) {
        CostMaterialDetail infraEntity = new CostMaterialDetail();
        BeanUtils.copyProperties(domainEntity, infraEntity);
        return infraEntity;
    }

    private CostMaterialDetailEntity convertToDomainMaterialDetailEntity(CostMaterialDetail infraEntity) {
        CostMaterialDetailEntity domainEntity = new CostMaterialDetailEntity();
        BeanUtils.copyProperties(infraEntity, domainEntity);
        return domainEntity;
    }

    @Override
    public List<CostProjectPlanEntity> queryByParentPlanId(String parentPlanId) {
        if (!StringUtils.hasText(parentPlanId)) {
            return List.of();
        }

        LambdaQueryWrapper<CostProjectPlan> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CostProjectPlan::getParentPlanId, parentPlanId);
        queryWrapper.orderByDesc(CostProjectPlan::getCreateTime);

        List<CostProjectPlan> infraList = costProjectPlanMapper.selectList(queryWrapper);
        return infraList.stream()
            .map(this::convertToDomainEntity)
            .collect(Collectors.toList());
    }
}
