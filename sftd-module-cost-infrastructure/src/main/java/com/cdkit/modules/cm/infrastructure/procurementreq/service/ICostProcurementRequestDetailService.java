package com.cdkit.modules.cm.infrastructure.procurementreq.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.procurementreq.entity.CostProcurementRequestDetail;

import java.util.List;

/**
 * @Description: 采购申请明细
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
public interface ICostProcurementRequestDetailService extends IService<CostProcurementRequestDetail> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostProcurementRequestDetail>
	 */
	public List<CostProcurementRequestDetail> selectByMainId(String mainId);
}
