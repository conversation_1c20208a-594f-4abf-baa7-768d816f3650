package com.cdkit.modules.cm.infrastructure.purchaseorderledger.repository;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.common.system.query.QueryGenerator;
import com.cdkit.common.util.SpringContextUtils;
import com.cdkit.common.util.oConvertUtils;
import com.cdkit.modules.cm.domain.purchaseorderledger.mode.entity.CostPurchaseOrderLedgerEntity;
import com.cdkit.modules.cm.domain.purchaseorderledger.repository.CostPurchaseOrderLedgerRepository;
import com.cdkit.modules.cm.infrastructure.purchaseorderledger.CostPurOrderLedgerInfraConverter;
import com.cdkit.modules.cm.infrastructure.purchaseorderledger.entity.CostPurchaseOrderLedger;
import com.cdkit.modules.cm.infrastructure.purchaseorderledger.service.ICostPurchaseOrderLedgerService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Repository
public class CostPurchaseOrderLedgerRepositoryImpl implements CostPurchaseOrderLedgerRepository {

    @Autowired
    ICostPurchaseOrderLedgerService costPurchaseOrderLedgerService;



    @Override
    public PageRes<CostPurchaseOrderLedgerEntity> page(CostPurchaseOrderLedgerEntity objectDTO, PageReq pageReq) {
        HttpServletRequest request = SpringContextUtils.getHttpServletRequest();
        CostPurchaseOrderLedger convert = CostPurOrderLedgerInfraConverter.convert(objectDTO);

        LambdaQueryWrapper<CostPurchaseOrderLedger> queryWrapper = QueryGenerator.initQueryWrapper(convert, request.getParameterMap()).lambda();
        queryWrapper.like(StrUtil.isNotEmpty(convert.getMaterialCode()),CostPurchaseOrderLedger::getMaterialCode, convert.getMaterialCode());
        queryWrapper.eq(StrUtil.isNotEmpty(convert.getOrderType()),CostPurchaseOrderLedger::getOrderType, convert.getOrderType());
        Page<CostPurchaseOrderLedger> page = new Page<>(pageReq.getCurrent(), pageReq.getSize());
        page.addOrder(BeanUtil.copyToList(pageReq.getOrderParam(), OrderItem.class));
        Page<CostPurchaseOrderLedger> pageList = costPurchaseOrderLedgerService.page(page, queryWrapper);
        List<CostPurchaseOrderLedger> records = pageList.getRecords();
        List<CostPurchaseOrderLedgerEntity> costPurchaseOrderLedgerDomains = CostPurOrderLedgerInfraConverter.convertList(records);
        return PageRes.of(pageReq.getCurrent(), pageReq.getSize(), costPurchaseOrderLedgerDomains,pageList.getTotal(), pageList.getPages());
    }

    @Override
    public int batchSave(List<CostPurchaseOrderLedgerEntity> domainList) {
        if (CollectionUtils.isEmpty(domainList)) {
            return 0;
        }

        List<CostPurchaseOrderLedger> entityList = new ArrayList<>();
        for (CostPurchaseOrderLedgerEntity domain : domainList) {
            CostPurchaseOrderLedger entity = CostPurOrderLedgerInfraConverter.convert(domain);
            entityList.add(entity);
        }

        // 使用MyBatis Plus的批量插入
        boolean result = costPurchaseOrderLedgerService.saveBatch(entityList);
        return result ? entityList.size() : 0;
    }


    @Override
    public List<CostPurchaseOrderLedgerEntity> list(String ids) {
        LambdaQueryWrapper<CostPurchaseOrderLedger> queryWrapper = new LambdaQueryWrapper<>();
        if (oConvertUtils.isNotEmpty(ids)) {
            List<String> selectionList = Arrays.asList(ids.split(","));
            queryWrapper.in(CostPurchaseOrderLedger::getId, selectionList);
        }
        List<CostPurchaseOrderLedger> list = costPurchaseOrderLedgerService.list(queryWrapper);
        return CostPurOrderLedgerInfraConverter.convertList(list);
    }
}
