package com.cdkit.modules.cm.infrastructure.annualbudget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.cdkit.modules.cm.infrastructure.annualbudget.entity.CostAnnualBudgetNonOperatingIndirectCost;

import java.util.List;

/**
 * @Description: 其他成本-非经营中心间接成本
 * @Author: cdkit-boot
 * @Date:   2025-07-30
 * @Version: V1.0
 */
public interface ICostAnnualBudgetNonOperatingIndirectCostService extends IService<CostAnnualBudgetNonOperatingIndirectCost> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<CostAnnualBudgetNonOperatingIndirectCost>
	 */
	public List<CostAnnualBudgetNonOperatingIndirectCost> selectByMainId(String mainId);
}
