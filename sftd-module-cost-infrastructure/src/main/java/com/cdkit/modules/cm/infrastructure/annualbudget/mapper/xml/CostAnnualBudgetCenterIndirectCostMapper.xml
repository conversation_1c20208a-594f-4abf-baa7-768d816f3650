<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdkit.modules.cm.infrastructure.annualbudget.mapper.CostAnnualBudgetCenterIndirectCostMapper">

	<delete id="deleteByMainId" parameterType="java.lang.String">
		DELETE 
		FROM  cost_annual_budget_center_indirect_cost 
		WHERE
			 budget_id = #{mainId} 	</delete>
	
	<select id="selectByMainId" parameterType="java.lang.String" resultType="com.cdkit.modules.cm.infrastructure.annualbudget.entity.CostAnnualBudgetCenterIndirectCost">
		SELECT * 
		FROM  cost_annual_budget_center_indirect_cost
		WHERE
			 budget_id = #{mainId} and del_flag=0 	</select>
</mapper>
