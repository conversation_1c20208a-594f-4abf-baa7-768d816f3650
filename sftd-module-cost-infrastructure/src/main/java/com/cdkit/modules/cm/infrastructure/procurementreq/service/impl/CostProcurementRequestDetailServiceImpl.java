package com.cdkit.modules.cm.infrastructure.procurementreq.service.impl;


import com.cdkit.modules.cm.infrastructure.procurementreq.entity.CostProcurementRequestDetail;
import com.cdkit.modules.cm.infrastructure.procurementreq.mapper.CostProcurementRequestDetailMapper;
import com.cdkit.modules.cm.infrastructure.procurementreq.service.ICostProcurementRequestDetailService;
import org.springframework.stereotype.Service;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Description: 采购申请明细
 * @Author: sunhzh
 * @Date:   2025-07-24
 * @Version: V1.0
 */
@Service
public class CostProcurementRequestDetailServiceImpl extends ServiceImpl<CostProcurementRequestDetailMapper, CostProcurementRequestDetail> implements ICostProcurementRequestDetailService {
	
	@Autowired
	private CostProcurementRequestDetailMapper costProcurementRequestDetailMapper;
	
	@Override
	public List<CostProcurementRequestDetail> selectByMainId(String mainId) {
		return costProcurementRequestDetailMapper.selectByMainId(mainId);
	}
}
