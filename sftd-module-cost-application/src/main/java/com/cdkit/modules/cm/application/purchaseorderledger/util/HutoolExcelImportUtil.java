package com.cdkit.modules.cm.application.purchaseorderledger.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.cdkit.common.exception.CdkitCloudException;
import com.cdkit.modules.cm.domain.purchaseorderledger.mode.entity.CostPurchaseOrderLedgerEntity;
import lombok.extern.slf4j.Slf4j;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * 基于Hutool的Excel导入工具类
 * 使用Hutool POI扩展简化Excel处理
 *
 * <AUTHOR>
 * @date 2025/07/14
 */
@Slf4j
public class HutoolExcelImportUtil {

    /**
     * 导入Excel数据
     *
     * @param inputStream Excel文件输入流
     * @param fileName    文件名（用于判断Excel版本）
     * @return 解析后的实体列表
     * @throws Exception 解析异常
     */
    public static List<CostPurchaseOrderLedgerEntity> importExcel(InputStream inputStream, String fileName) throws Exception {
        List<CostPurchaseOrderLedgerEntity> resultList = new ArrayList<>();

        try {
            // 使用Hutool创建ExcelReader
            ExcelReader reader = ExcelUtil.getReader(inputStream);

            // 设置表头别名，将Excel表头映射到实体字段
            setupHeaderAlias(reader);

            // 读取所有数据（跳过表头）
            List<Map<String, Object>> rows = reader.readAll();

            log.info("从Excel中读取到{}行数据", rows.size());

            // 转换为实体对象
            for (int i = 0; i < rows.size(); i++) {
                Map<String, Object> row = rows.get(i);
                try {
                    CostPurchaseOrderLedgerEntity entity = convertRowToEntity(row, i + 2); // 第2行开始是数据行
                    if (entity != null) {
                        resultList.add(entity);
                    }
                } catch (Exception e) {
                    log.warn("解析第{}行数据失败: {}", i + 2, e.getMessage());
                    // 继续处理下一行，不中断整个导入过程
                }
            }

            log.info("成功解析{}行数据", resultList.size());
            return resultList;

        } catch (Exception e) {
            log.error("Excel解析失败", e);
            throw new Exception("Excel文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 设置表头别名，将Excel表头映射到实体字段
     */
    private static void setupHeaderAlias(ExcelReader reader) {
        // 设置表头别名，将Excel中的中文表头映射到实体字段名
        reader.addHeaderAlias("签订时间", "signTime");
        reader.addHeaderAlias("计划交货日期/验收日期", "plannedDeliveryDate");
        reader.addHeaderAlias("订单类型", "orderType");
        reader.addHeaderAlias("需求编号", "demandNumber");
        reader.addHeaderAlias("立项号", "projectNumber");
        reader.addHeaderAlias("PR号", "prNumber");
        reader.addHeaderAlias("订单名称", "orderName");
        reader.addHeaderAlias("立项名称", "projectName");
        reader.addHeaderAlias("原材料/服务名称", "materialName");
        reader.addHeaderAlias("包装要求/规格", "specification");
        reader.addHeaderAlias("物资编码", "materialCode");
        reader.addHeaderAlias("单位", "unit");
        reader.addHeaderAlias("数量", "quantity");
        reader.addHeaderAlias("预算单价", "budgetUnitPrice");
        reader.addHeaderAlias("预算总价", "budgetTotalPrice");
        reader.addHeaderAlias("协议单价", "agreementUnitPrice");
        reader.addHeaderAlias("协议总价", "agreementTotalPrice");
        reader.addHeaderAlias("订单单价", "orderUnitPrice");
        reader.addHeaderAlias("订单不含税单价", "orderExcludingTaxPrice");
        reader.addHeaderAlias("订单金额", "orderAmount");
        reader.addHeaderAlias("匹配协议编号", "matchedAgreementNumber");
        reader.addHeaderAlias("协议类型", "agreementType");
        reader.addHeaderAlias("订单号", "orderNumber");
        reader.addHeaderAlias("SAP订单号", "sapOrderNumber");
        reader.addHeaderAlias("供应商", "supplier");
        reader.addHeaderAlias("执行区域", "executionRegion");
        reader.addHeaderAlias("申请单位", "applicationUnit");
    }

    /**
     * 将行数据转换为实体对象
     */
    private static CostPurchaseOrderLedgerEntity convertRowToEntity(Map<String, Object> row, int rowNum) {
        CostPurchaseOrderLedgerEntity entity = new CostPurchaseOrderLedgerEntity();

        try {
            // 解析日期字段
            entity.setSignTime(parseDate(row.get("signTime")));
            entity.setPlannedDeliveryDate(parseDate(row.get("plannedDeliveryDate")));

            // 解析字符串字段
            entity.setOrderType(parseString(row.get("orderType")));
            entity.setDemandNumber(parseString(row.get("demandNumber")));
            entity.setProjectNumber(parseString(row.get("projectNumber")));
            entity.setPrNumber(parseString(row.get("prNumber")));
            entity.setOrderName(parseString(row.get("orderName")));
            entity.setProjectName(parseString(row.get("projectName")));
            entity.setMaterialName(parseString(row.get("materialName")));
            entity.setSpecification(parseString(row.get("specification")));
            entity.setMaterialCode(parseString(row.get("materialCode")));
            entity.setUnit(parseString(row.get("unit")));
            entity.setMatchedAgreementNumber(parseString(row.get("matchedAgreementNumber")));
            entity.setAgreementType(parseString(row.get("agreementType")));
            entity.setOrderNumber(parseString(row.get("orderNumber")));
            entity.setSapOrderNumber(parseString(row.get("sapOrderNumber")));
            entity.setSupplier(parseString(row.get("supplier")));
            entity.setExecutionRegion(parseString(row.get("executionRegion")));
            entity.setApplicationUnit(parseString(row.get("applicationUnit")));

            // 解析数值字段
            entity.setQuantity(parseBigDecimal(row.get("quantity")));
            entity.setBudgetUnitPrice(parseBigDecimal(row.get("budgetUnitPrice")));
            entity.setBudgetTotalPrice(parseBigDecimal(row.get("budgetTotalPrice")));
            entity.setAgreementUnitPrice(parseBigDecimal(row.get("agreementUnitPrice")));
            entity.setAgreementTotalPrice(parseBigDecimal(row.get("agreementTotalPrice")));
            entity.setOrderUnitPrice(parseBigDecimal(row.get("orderUnitPrice")));
            entity.setOrderExcludingTaxPrice(parseBigDecimal(row.get("orderExcludingTaxPrice")));
            entity.setOrderAmount(parseBigDecimal(row.get("orderAmount")));

            // 设置创建时间
            entity.setCreateTime(new Date());

            return entity;

        } catch (Exception e) {
            throw new CdkitCloudException("解析第" + rowNum + "行数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析字符串值
     */
    private static String parseString(Object value) {
        if (value == null) {
            return null;
        }
        String str = StrUtil.toString(value).trim();
        return StrUtil.isBlank(str) ? null : str;
    }

    /**
     * 解析BigDecimal值
     */
    private static BigDecimal parseBigDecimal(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Number) {
                return new BigDecimal(value.toString());
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (StrUtil.isBlank(strValue)) {
                    return null;
                }
                return new BigDecimal(strValue);
            }
        } catch (Exception e) {
            log.warn("解析数值失败: {}", e.getMessage());
        }

        return null;
    }

    /**
     * 解析日期值
     */
    private static Date parseDate(Object value) {
        if (value == null) {
            return null;
        }

        try {
            if (value instanceof Date) {
                return (Date) value;
            } else if (value instanceof String) {
                String strValue = ((String) value).trim();
                if (StrUtil.isBlank(strValue)) {
                    return null;
                }
                // 使用Hutool的日期解析，支持多种格式
                return DateUtil.parse(strValue);
            }
        } catch (Exception e) {
            log.warn("解析日期失败: {}", e.getMessage());
        }

        return null;
    }

}
