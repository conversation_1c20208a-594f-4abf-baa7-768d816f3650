package com.cdkit.modules.cm.application.annualbudget;

import com.cdkit.common.page.OrderParam;
import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.annualbudget.mode.entity.CostAnnualBudgetEntity;
import com.cdkit.modules.cm.domain.annualbudget.repository.CostAnnualBudgetRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * 年度总预算应用服务
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AnnualBudgetApplication {

    private final CostAnnualBudgetRepository costAnnualBudgetRepository;

    /**
     * 分页查询年度总预算列表
     *
     * @param queryEntity 查询条件
     * @param pageNo 页码
     * @param pageSize 每页数量
     * @return 分页结果
     */
    public PageRes<CostAnnualBudgetEntity> queryPageList(CostAnnualBudgetEntity queryEntity, Integer pageNo, Integer pageSize) {
        PageReq pageReq = new PageReq();
        pageReq.setCurrent((long) pageNo);
        pageReq.setSize((long) pageSize);

        // 按照创建时间倒序
        OrderParam createTimeParam = new OrderParam();
        createTimeParam.setField("create_time");
        createTimeParam.setOrder("desc");
        pageReq.setOrderParam(Arrays.asList(createTimeParam));

        return costAnnualBudgetRepository.queryPageList(queryEntity, pageReq);
    }

    /**
     * 根据ID查询年度总预算详情
     *
     * @param id 年度总预算ID
     * @return 年度总预算详情
     */
    public CostAnnualBudgetEntity queryById(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }
        return costAnnualBudgetRepository.findById(id);
    }

    /**
     * 新增年度总预算
     *
     * @param entity 年度总预算实体
     * @return 年度总预算ID
     */
    public String add(CostAnnualBudgetEntity entity) {
        if (entity == null) {
            throw new IllegalArgumentException("年度总预算数据不能为空");
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证预算编号唯一性
        if (StringUtils.hasText(entity.getBudgetCode())) {
            CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findByBudgetCode(entity.getBudgetCode());
            if (existingEntity != null) {
                throw new IllegalArgumentException("预算编号已存在：" + entity.getBudgetCode());
            }
        }

        // 计算剩余金额
        entity.calculateRevenueRemainingAmount();
        entity.calculateDirectCostRemainingAmount();

        CostAnnualBudgetEntity savedEntity = costAnnualBudgetRepository.save(entity);
        log.info("新增年度总预算成功，ID: {}, 预算编号: {}", savedEntity.getId(), savedEntity.getBudgetCode());
        
        return savedEntity.getId();
    }

    /**
     * 编辑年度总预算
     *
     * @param entity 年度总预算实体
     * @return 年度总预算ID
     */
    public String edit(CostAnnualBudgetEntity entity) {
        if (entity == null || !StringUtils.hasText(entity.getId())) {
            throw new IllegalArgumentException("年度总预算数据或ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(entity.getId());
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + entity.getId());
        }

        // 验证是否可以修改
        if (!existingEntity.canModify()) {
            throw new IllegalArgumentException("当前状态不允许修改，状态: " + existingEntity.getBudgetStatus());
        }

        // 验证必填字段
        validateRequiredFields(entity);

        // 验证预算编号唯一性（排除自己）
        if (StringUtils.hasText(entity.getBudgetCode()) && !entity.getBudgetCode().equals(existingEntity.getBudgetCode())) {
            CostAnnualBudgetEntity duplicateEntity = costAnnualBudgetRepository.findByBudgetCode(entity.getBudgetCode());
            if (duplicateEntity != null && !duplicateEntity.getId().equals(entity.getId())) {
                throw new IllegalArgumentException("预算编号已存在：" + entity.getBudgetCode());
            }
        }

        // 计算剩余金额
        entity.calculateRevenueRemainingAmount();
        entity.calculateDirectCostRemainingAmount();

        CostAnnualBudgetEntity updatedEntity = costAnnualBudgetRepository.updateById(entity);
        log.info("编辑年度总预算成功，ID: {}, 预算编号: {}", updatedEntity.getId(), updatedEntity.getBudgetCode());
        
        return updatedEntity.getId();
    }

    /**
     * 根据ID删除年度总预算
     *
     * @param id 年度总预算ID
     */
    public void delete(String id) {
        if (!StringUtils.hasText(id)) {
            throw new IllegalArgumentException("年度总预算ID不能为空");
        }

        // 验证记录是否存在
        CostAnnualBudgetEntity existingEntity = costAnnualBudgetRepository.findById(id);
        if (existingEntity == null) {
            throw new IllegalArgumentException("年度总预算不存在，ID: " + id);
        }

        // 验证是否可以删除
        if (!existingEntity.canDelete()) {
            throw new IllegalArgumentException("当前状态不允许删除，状态: " + existingEntity.getBudgetStatus());
        }

        costAnnualBudgetRepository.deleteById(id);
        log.info("删除年度总预算成功，ID: {}, 预算编号: {}", id, existingEntity.getBudgetCode());
    }

    /**
     * 批量删除年度总预算
     *
     * @param ids 年度总预算ID列表，逗号分隔
     */
    public void deleteBatch(String ids) {
        if (!StringUtils.hasText(ids)) {
            throw new IllegalArgumentException("年度总预算ID列表不能为空");
        }

        List<String> idList = Arrays.asList(ids.split(","));
        
        // 验证每个记录是否可以删除
        List<CostAnnualBudgetEntity> entities = costAnnualBudgetRepository.findByIds(idList);
        for (CostAnnualBudgetEntity entity : entities) {
            if (!entity.canDelete()) {
                throw new IllegalArgumentException("预算编号 " + entity.getBudgetCode() + " 当前状态不允许删除，状态: " + entity.getBudgetStatus());
            }
        }

        costAnnualBudgetRepository.deleteByIds(idList);
        log.info("批量删除年度总预算成功，删除数量: {}", idList.size());
    }

    /**
     * 验证必填字段
     */
    private void validateRequiredFields(CostAnnualBudgetEntity entity) {
        if (!StringUtils.hasText(entity.getBudgetName())) {
            throw new IllegalArgumentException("总预算名称不能为空");
        }
        if (!StringUtils.hasText(entity.getBudgetYear())) {
            throw new IllegalArgumentException("年份不能为空");
        }
        if (!StringUtils.hasText(entity.getProfessionalCompany())) {
            throw new IllegalArgumentException("所属单位不能为空");
        }
    }
}
