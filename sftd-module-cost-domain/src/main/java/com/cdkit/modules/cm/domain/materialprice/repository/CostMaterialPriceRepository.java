package com.cdkit.modules.cm.domain.materialprice.repository;

import com.cdkit.common.page.PageReq;
import com.cdkit.common.page.PageRes;
import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.materialprice.mode.entity.CostMaterialPriceEntity;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * 材料单价仓储接口
 * <AUTHOR>
 * @date 2025/07/16
 */
public interface CostMaterialPriceRepository extends IBaseDomainRepository<CostMaterialPriceEntity,CostMaterialPriceEntity> {

    CostMaterialPriceEntity save(CostMaterialPriceEntity entity);

    CostMaterialPriceEntity updateById(CostMaterialPriceEntity entity);

    void deleteById(String id);

    void deleteByIds(List<String> ids);

    CostMaterialPriceEntity findById(String id);

    List<CostMaterialPriceEntity> findByIds(List<String> ids);

    /**
     * 分页查询材料单价
     *
     * @param queryEntity 查询条件
     * @param pageReq 分页参数
     * @return 分页结果
     */
    PageRes<CostMaterialPriceEntity> queryPageList(CostMaterialPriceEntity queryEntity, PageReq pageReq);

    /**
     * 查询材料单价列表（不分页）
     *
     * @param queryEntity 查询条件
     * @return 查询结果列表
     */
    List<CostMaterialPriceEntity> queryList(CostMaterialPriceEntity queryEntity);

    /**
     * 根据物料编码获取最新的材料单价记录
     *
     * @param materialCode 物料编码
     * @return 最新的材料单价记录
     */
    CostMaterialPriceEntity getLatestByMaterialCode(String materialCode);

    /**
     * 根据物料编码查询在指定时间段内生效的记录
     *
     * @param materialCode 物料编码
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 生效的记录列表
     */
    List<CostMaterialPriceEntity> findEffectiveInPeriod(String materialCode, Date startDate, Date endDate);

    /**
     * 根据物料编码查询当前生效的记录
     *
     * @param materialCode 物料编码
     * @return 当前生效的记录列表
     */
    List<CostMaterialPriceEntity> findCurrentEffective(String materialCode);

    /**
     * 批量更新状态为已失效
     *
     * @param ids ID列表
     */
    void batchSetExpired(List<String> ids);

    /**
     * 根据状态查询记录
     *
     * @param status 状态
     * @return 记录列表
     */
    List<CostMaterialPriceEntity> findByStatus(String status);

    /**
     * 查询需要更新状态的记录（根据生效失效日期）
     *
     * @return 需要更新状态的记录列表
     */
    List<CostMaterialPriceEntity> findNeedUpdateStatus();
}
