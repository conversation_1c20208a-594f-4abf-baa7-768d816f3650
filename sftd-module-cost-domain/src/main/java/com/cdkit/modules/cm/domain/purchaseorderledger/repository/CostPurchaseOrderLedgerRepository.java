package com.cdkit.modules.cm.domain.purchaseorderledger.repository;

import com.cdkit.modules.cm.domain.IBaseDomainRepository;
import com.cdkit.modules.cm.domain.purchaseorderledger.mode.entity.CostPurchaseOrderLedgerEntity;

import java.util.List;

/**
 * 采购订单台账仓储层
 * <AUTHOR>
 * @date 2025/07/09
 */
public interface CostPurchaseOrderLedgerRepository  extends IBaseDomainRepository<CostPurchaseOrderLedgerEntity, CostPurchaseOrderLedgerEntity> {

    /**
     * 批量保存采购订单台账数据
     *
     * @param domainList 采购订单台账领域对象列表
     * @return 保存成功的数量
     */
    int batchSave(List<CostPurchaseOrderLedgerEntity> domainList);

    List<CostPurchaseOrderLedgerEntity> list(String ids);
}
