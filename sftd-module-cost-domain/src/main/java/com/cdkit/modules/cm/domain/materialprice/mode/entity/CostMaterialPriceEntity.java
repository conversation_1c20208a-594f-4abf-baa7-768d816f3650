package com.cdkit.modules.cm.domain.materialprice.mode.entity;

import com.cdkit.modules.cm.domain.materialprice.valobj.MaterialPriceStatusEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * 材料单价领域实体
 * <AUTHOR>
 * @date 2025/07/16
 */
@Data
@Accessors(chain = true)
public class CostMaterialPriceEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    private String id;

    /**物料名称*/
    private String materialName;

    /**物料编码*/
    private String materialCode;

    /**状态: pending_submit-待提交, not_effective-未生效, in_effect-生效中, expired-已失效*/
    private String materialPriceStatus;

    /**含税单价*/
    private BigDecimal taxUnitPrice;

    /**不含税单价*/
    private BigDecimal nonTaxUnitPrice;

    /**类型: fixed-固定, float-浮动*/
    private String priceType;

    /**上浮比例*/
    private BigDecimal upRate;

    /**来源*/
    private String source;

    /**生效日期*/
    private Date effectiveDate;

    /**失效日期*/
    private Date expirationDate;

    /**税率(%)*/
    private BigDecimal taxRate;

    /**创建时间*/
    private Date createTime;

    /**创建人*/
    private String createBy;

    /**更新时间*/
    private Date updateTime;

    /**更新人*/
    private String updateBy;

    /**租户ID*/
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    private Integer delFlag;

    /**
     * 默认税率13%
     */
    private static final BigDecimal DEFAULT_TAX_RATE = new BigDecimal("13.00");

    /**
     * 初始化默认值
     */
    public void initDefaults() {
        if (this.taxRate == null) {
            this.taxRate = DEFAULT_TAX_RATE;
        }
        if (this.materialPriceStatus == null) {
            this.materialPriceStatus = MaterialPriceStatusEnum.PENDING_SUBMIT.getCode();
        }
        if (this.delFlag == null) {
            this.delFlag = 0;
        }
    }

    /**
     * 根据含税单价计算不含税单价
     */
    public void calculateNonTaxPrice() {
        if (this.taxUnitPrice != null && this.taxRate != null) {
            BigDecimal divisor = BigDecimal.ONE.add(this.taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
            this.nonTaxUnitPrice = this.taxUnitPrice.divide(divisor, 2, RoundingMode.HALF_UP);
        }
    }

    /**
     * 根据不含税单价计算含税单价
     */
    public void calculateTaxPrice() {
        if (this.nonTaxUnitPrice != null && this.taxRate != null) {
            BigDecimal multiplier = BigDecimal.ONE.add(this.taxRate.divide(new BigDecimal("100"), 4, RoundingMode.HALF_UP));
            this.taxUnitPrice = this.nonTaxUnitPrice.multiply(multiplier).setScale(2, RoundingMode.HALF_UP);
        }
    }

    /**
     * 判断是否可以编辑
     */
    public boolean canEdit() {
        return MaterialPriceStatusEnum.canEdit(this.materialPriceStatus);
    }

    /**
     * 判断是否可以删除
     */
    public boolean canDelete() {
        return MaterialPriceStatusEnum.canDelete(this.materialPriceStatus);
    }

    /**
     * 判断是否可以提交
     */
    public boolean canSubmit() {
        return MaterialPriceStatusEnum.canSubmit(this.materialPriceStatus);
    }

    /**
     * 提交材料单价
     */
    public void submit() {
        if (!canSubmit()) {
            throw new IllegalStateException("当前状态不允许提交");
        }

        Date now = new Date();
        if (this.effectiveDate == null) {
            throw new IllegalArgumentException("生效日期不能为空");
        }

        // 如果失效日期不为空，校验生效日期不能晚于失效日期
        if (this.expirationDate != null && this.effectiveDate.after(this.expirationDate)) {
            throw new IllegalArgumentException("生效日期不能晚于失效日期");
        }

        // 根据生效日期和失效日期判断状态
        if (now.before(this.effectiveDate)) {
            // 未到生效日期，状态为未生效
            this.materialPriceStatus = MaterialPriceStatusEnum.NOT_EFFECTIVE.getCode();
        } else if (this.expirationDate != null && now.after(this.expirationDate)) {
            // 超过失效日期，状态为已失效
            this.materialPriceStatus = MaterialPriceStatusEnum.EXPIRED.getCode();
        } else {
            // 在生效期内（失效日期为空时认为一直生效），状态为生效中
            this.materialPriceStatus = MaterialPriceStatusEnum.IN_EFFECT.getCode();
        }
    }

    /**
     * 更新状态（根据当前时间和生效失效日期）
     */
    public void updateStatus() {
        if (MaterialPriceStatusEnum.PENDING_SUBMIT.getCode().equals(this.materialPriceStatus)) {
            return; // 待提交状态不自动更新
        }

        Date now = new Date();
        if (this.effectiveDate != null) {
            if (now.before(this.effectiveDate)) {
                // 未到生效日期，状态为未生效
                this.materialPriceStatus = MaterialPriceStatusEnum.NOT_EFFECTIVE.getCode();
            } else if (this.expirationDate != null && now.after(this.expirationDate)) {
                // 超过失效日期，状态为已失效
                this.materialPriceStatus = MaterialPriceStatusEnum.EXPIRED.getCode();
            } else {
                // 在生效期内（失效日期为空时认为一直生效），状态为生效中
                this.materialPriceStatus = MaterialPriceStatusEnum.IN_EFFECT.getCode();
            }
        }
    }

    /**
     * 设置为已失效
     */
    public void setExpired() {
        this.materialPriceStatus = MaterialPriceStatusEnum.EXPIRED.getCode();
    }

    /**
     * 判断是否在生效期内
     */
    public boolean isInEffect() {
        return MaterialPriceStatusEnum.IN_EFFECT.getCode().equals(this.materialPriceStatus);
    }

    /**
     * 判断是否在指定时间段内生效
     */
    public boolean isEffectiveInPeriod(Date startDate, Date endDate) {
        if (this.effectiveDate == null || this.expirationDate == null) {
            return false;
        }
        
        // 判断时间段是否有重叠
        return !(this.expirationDate.before(startDate) || this.effectiveDate.after(endDate));
    }
}
