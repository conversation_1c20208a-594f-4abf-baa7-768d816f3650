package com.cdkit.modules.cm.domain.projectplan.mode.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;

/**
 * @Description: 原料明细领域实体
 * @Author: cdkit-boot
 * @Date: 2025-07-24
 * @Version: V1.0
 */
@Schema(description = "原料明细领域实体")
@Data
public class CostMaterialDetailEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**UUID主键*/
    @Schema(description = "UUID主键")
    private String id;

    /**关联计划ID*/
    @Schema(description = "关联计划ID")
    private String planId;

    /**物料编码*/
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;

    /**物料名称*/
    @Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称")
    private String materialName;

    /**用量*/
    @Excel(name = "用量", width = 15)
    @Schema(description = "用量")
    private BigDecimal usageAmount;

    /**单位*/
    @Excel(name = "单位", width = 15)
    @Schema(description = "单位")
    private String unit;

    /**税率(%)*/
    @Excel(name = "税率(%)", width = 15)
    @Schema(description = "税率(%)")
    private BigDecimal taxRate;

    /**含税单价(元)*/
    @Excel(name = "含税单价(元)", width = 15)
    @Schema(description = "含税单价(元)")
    private BigDecimal unitPriceIncludingTax;

    /**不含税单价(元)*/
    @Excel(name = "不含税单价(元)", width = 15)
    @Schema(description = "不含税单价(元)")
    private BigDecimal unitPriceExcludingTax;

    /**含税总价(元)*/
    @Excel(name = "含税总价(元)", width = 15)
    @Schema(description = "含税总价(元)")
    private BigDecimal totalPriceIncludingTax;

    /**不含税总价(元)*/
    @Excel(name = "不含税总价(元)", width = 15)
    @Schema(description = "不含税总价(元)")
    private BigDecimal totalPriceExcludingTax;

    /**备注*/
    @Excel(name = "备注", width = 15)
    @Schema(description = "备注")
    private String remark;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    @Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;

    /**所属部门代码*/
    @Schema(description = "所属部门代码")
    private String sysOrgCode;

    /**
     * 计算含税总价和不含税总价
     * 含税总价 = 用量 × 含税单价
     * 不含税总价 = 用量 × 不含税单价
     */
    public void calculateTotalPrice() {
        if (usageAmount != null) {
            if (unitPriceIncludingTax != null) {
                this.totalPriceIncludingTax = usageAmount.multiply(unitPriceIncludingTax)
                    .setScale(2, RoundingMode.HALF_UP);
            } else {
                this.totalPriceIncludingTax = BigDecimal.ZERO;
            }

            if (unitPriceExcludingTax != null) {
                this.totalPriceExcludingTax = usageAmount.multiply(unitPriceExcludingTax)
                    .setScale(2, RoundingMode.HALF_UP);
            } else {
                this.totalPriceExcludingTax = BigDecimal.ZERO;
            }
        } else {
            this.totalPriceIncludingTax = BigDecimal.ZERO;
            this.totalPriceExcludingTax = BigDecimal.ZERO;
        }
    }

    /**
     * 验证数据有效性
     * @return true-有效，false-无效
     */
    public boolean isValid() {
        return materialCode != null && !materialCode.trim().isEmpty() 
            && materialName != null && !materialName.trim().isEmpty()
            && usageAmount != null && usageAmount.compareTo(BigDecimal.ZERO) >= 0;
    }

    /**
     * 初始化默认值
     */
    public void initDefaults() {
        if (this.usageAmount == null) {
            this.usageAmount = BigDecimal.ZERO;
        }
        if (this.taxRate == null) {
            this.taxRate = new BigDecimal("13.00"); // 默认税率13%
        }
        if (this.unitPriceIncludingTax == null) {
            this.unitPriceIncludingTax = BigDecimal.ZERO;
        }
        if (this.unitPriceExcludingTax == null) {
            this.unitPriceExcludingTax = BigDecimal.ZERO;
        }
        if (this.totalPriceIncludingTax == null) {
            this.totalPriceIncludingTax = BigDecimal.ZERO;
        }
        if (this.totalPriceExcludingTax == null) {
            this.totalPriceExcludingTax = BigDecimal.ZERO;
        }
        if (this.delFlag == null) {
            this.delFlag = 0;
        }
        if (this.unit == null || this.unit.trim().isEmpty()) {
            this.unit = "吨"; // 默认单位
        }
    }
}
