package com.cdkit.modules.cm.performance.schedule;

import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.application.project.ProjectApplication;
import com.cdkit.modules.cm.application.schedule.ProjectScheduleTask;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 定时任务测试控制器
 * 用于手动测试定时任务功能
 * 注意：此控制器仅用于开发测试，生产环境应移除
 * 
 * <AUTHOR>
 * @date 2025/07/14
 */
@Tag(name = "定时任务测试", description = "定时任务测试相关接口")
@RestController
@RequestMapping("/api/schedule/test")
@RequiredArgsConstructor
@Slf4j
public class ScheduleTestController {

    private final ProjectApplication projectApplication;
    private final ProjectScheduleTask projectScheduleTask;

    @Operation(summary = "手动执行项目自动关闭定时任务")
    @PostMapping("/close-last-year-projects")
    public Result<String> closeLastYearProjects() {
        try {
            log.info("手动触发项目自动关闭定时任务");
            projectScheduleTask.closeLastYearProjects();
            return Result.OK("定时任务执行完成");
        } catch (Exception e) {
            log.error("手动执行定时任务失败", e);
            return Result.error("定时任务执行失败：" + e.getMessage());
        }
    }

    @Operation(summary = "手动关闭指定年度的执行中项目")
    @PostMapping("/close-projects-by-year")
    public Result<String> closeProjectsByYear(@RequestParam String projectYear) {
        try {
            log.info("手动关闭{}年度的执行中项目", projectYear);
            int closedCount = projectApplication.closeInExecutionProjectsByYear(projectYear);
            return Result.OK("成功关闭" + closedCount + "个项目");
        } catch (Exception e) {
            log.error("手动关闭项目失败", e);
            return Result.error("关闭项目失败：" + e.getMessage());
        }
    }

    @Operation(summary = "查询指定年度执行中的项目数量")
    @GetMapping("/count-in-execution-projects")
    public Result<Integer> countInExecutionProjects(@RequestParam String projectYear) {
        try {
            // 这里需要在Repository中添加计数方法，暂时返回列表大小
            int count = projectApplication.queryInExecutionProjectsByYear(projectYear).size();
            return Result.OK(count);
        } catch (Exception e) {
            log.error("查询执行中项目数量失败", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    @Operation(summary = "获取当前年度和上一年度信息")
    @GetMapping("/year-info")
    public Result<String> getYearInfo() {
        int currentYear = LocalDate.now().getYear();
        int lastYear = currentYear - 1;
        String info = String.format("当前年度：%d，上一年度：%d", currentYear, lastYear);
        return Result.OK(info);
    }

    @Operation(summary = "手动执行测试定时任务")
    @PostMapping("/test-schedule-task")
    public Result<String> testScheduleTask() {
        try {
            log.info("手动触发测试定时任务");
            projectScheduleTask.testScheduleTask();
            return Result.OK("测试定时任务执行完成");
        } catch (Exception e) {
            log.error("手动执行测试定时任务失败", e);
            return Result.error("测试定时任务执行失败：" + e.getMessage());
        }
    }
}
