package com.cdkit.modules.cm.performance.annualbudget;

import com.cdkit.modules.cm.api.annualbudget.dto.CostAnnualBudgetDTO;
import com.cdkit.modules.cm.domain.annualbudget.mode.entity.CostAnnualBudgetEntity;
import org.springframework.beans.BeanUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 年度总预算转换器
 * <AUTHOR>
 * @date 2025-07-30
 */
public class CostAnnualBudgetConverter {

    /**
     * DTO转换为领域实体
     *
     * @param dto DTO对象
     * @return 领域实体
     */
    public static CostAnnualBudgetEntity toEntity(CostAnnualBudgetDTO dto) {
        if (dto == null) {
            return null;
        }
        
        CostAnnualBudgetEntity entity = new CostAnnualBudgetEntity();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }

    /**
     * 领域实体转换为DTO
     *
     * @param entity 领域实体
     * @return DTO对象
     */
    public static CostAnnualBudgetDTO toDTO(CostAnnualBudgetEntity entity) {
        if (entity == null) {
            return null;
        }
        
        CostAnnualBudgetDTO dto = new CostAnnualBudgetDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 领域实体列表转换为DTO列表
     *
     * @param entityList 领域实体列表
     * @return DTO列表
     */
    public static List<CostAnnualBudgetDTO> toDTOList(List<CostAnnualBudgetEntity> entityList) {
        if (entityList == null || entityList.isEmpty()) {
            return null;
        }
        
        return entityList.stream()
                .map(CostAnnualBudgetConverter::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * DTO列表转换为领域实体列表
     *
     * @param dtoList DTO列表
     * @return 领域实体列表
     */
    public static List<CostAnnualBudgetEntity> toEntityList(List<CostAnnualBudgetDTO> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) {
            return null;
        }
        
        return dtoList.stream()
                .map(CostAnnualBudgetConverter::toEntity)
                .collect(Collectors.toList());
    }
}
