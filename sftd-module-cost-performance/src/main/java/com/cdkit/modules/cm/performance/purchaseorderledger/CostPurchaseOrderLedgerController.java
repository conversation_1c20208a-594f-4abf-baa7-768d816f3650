package com.cdkit.modules.cm.performance.purchaseorderledger;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.common.page.PageRes;

import com.cdkit.modules.cm.api.purchaseorderledger.IPurchaseOrderLedgerApi;
import com.cdkit.modules.cm.api.purchaseorderledger.dto.CostPurchaseOrderLedgerDTO;
import com.cdkit.modules.cm.api.common.ExcelImportResult;
import com.cdkit.modules.cm.application.purchaseorderledger.PurchaseOrderLedgerApplication;
import com.cdkit.modules.cm.domain.purchaseorderledger.mode.entity.CostPurchaseOrderLedgerEntity;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;


/**
 zhaoyang
 * @Description: 采购订单台账
 * @Author: cdkit-boot
 * @Date:   2025-07-09
 * @Version: V1.0
 */
@Tag(name="采购订单台账")
@RestController
@RequestMapping("/purchase/costPurchaseOrderLedger")
@Slf4j
public class CostPurchaseOrderLedgerController  implements IPurchaseOrderLedgerApi {

   private final PurchaseOrderLedgerApplication purchaseOrderLedgerApplication;

    public CostPurchaseOrderLedgerController(PurchaseOrderLedgerApplication purchaseOrderLedgerApplication) {
        this.purchaseOrderLedgerApplication = purchaseOrderLedgerApplication;
    }


    @Operation(summary="采购订单台账-分页列表查询")
	@Override
	public Result<IPage<CostPurchaseOrderLedgerDTO>> queryPageList(CostPurchaseOrderLedgerDTO queryVO, @RequestParam(name="pageNo", defaultValue="1") Integer pageNo, @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
		CostPurchaseOrderLedgerEntity buildDomain = CostPurchaseOrderLedgerEntity.builder().materialCode(queryVO.getMaterialCode()).orderType(queryVO.getOrderType()).build();
		PageRes<CostPurchaseOrderLedgerEntity> queryPageList = purchaseOrderLedgerApplication.queryPageList(buildDomain, pageNo, pageSize);

        // 使用 MyBatis Plus 的分页对象
        IPage<CostPurchaseOrderLedgerDTO> page = new Page<>(pageNo, pageSize);

        if (queryPageList != null) {
			page.setCurrent(queryPageList.getCurrent());
			page.setSize(queryPageList.getSize());
			page.setTotal(queryPageList.getTotal());
			page.setRecords(CostPurchaseOrderLedgerConverter.toVOList(queryPageList.getRecords()));
		}

		return Result.OK(page);
	}

    @Operation(summary="采购订单台账-Excel导入")
    @Override
    public Result<ExcelImportResult> importExcel(HttpServletRequest request, HttpServletResponse response) {
        try {
            ExcelImportResult importResult = purchaseOrderLedgerApplication.importExcel(request);
            return Result.OK(importResult);
        } catch (Exception e) {
            log.error("Excel导入失败", e);
            return Result.error("Excel导入失败：" + e.getMessage());
        }
    }

    @Operation(summary="采购订单台账-导出Excel模板")
    @Override
    public void exportTemplate(HttpServletResponse response) {
        try {
            // 设置Excel响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            String fileName = URLEncoder.encode("采购订单台账导入模板", StandardCharsets.UTF_8);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + ".xlsx");

            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("采购订单台账");

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerStyle.setFont(headerFont);
            headerStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 定义表头（与实体类@Excel注解名称保持一致）
            String[] headers = {
                "签订时间", "计划交货日期/验收日期", "订单类型", "需求编号", "立项号", "PR号",
                "订单名称", "立项名称", "原材料/服务名称", "包装要求/规格", "物资编码", "单位", "数量",
                "预算单价", "预算总价", "协议单价", "协议总价", "订单单价", "订单不含税单价",
                "订单金额", "匹配协议编号", "协议类型", "订单号", "SAP订单号", "供应商",
                "执行区域", "申请单位"
            };

            // 创建表头行
            Row headerRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
                sheet.setColumnWidth(i, 4000); // 设置列宽
            }

            // 创建示例数据行
            Row exampleRow = sheet.createRow(1);
            String[] exampleData = {
                "2025-07-11", "2025-07-20", "采购订单", "REQ001", "PRJ001", "PR001",
                "示例订单", "示例立项", "示例原材料", "示例包装要求", "MAT001", "个", "100",
                "10.50", "1050.00", "10.00", "1000.00", "10.20", "9.50",
                "1020.00", "AGR001", "框架协议", "ORD001", "SAP001", "示例供应商",
                "华南区域", "采购部"
            };

            for (int i = 0; i < exampleData.length; i++) {
                Cell cell = exampleRow.createCell(i);
                cell.setCellValue(exampleData[i]);
            }

            // 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
            response.getOutputStream().flush();

        } catch (Exception e) {
            log.error("导出Excel模板失败", e);
            try {
                response.reset();
                response.setContentType("application/json");
                response.setCharacterEncoding("UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"导出失败：" + e.getMessage() + "\"}");
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }

}
