package com.cdkit.modules.cm.api.purchaseorderledger;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.purchaseorderledger.dto.CostPurchaseOrderLedgerDTO;
import com.cdkit.modules.cm.api.common.ExcelImportResult;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * 采购订单台账api
 * <AUTHOR>
 * @date 2025/07/10
 */
public interface IPurchaseOrderLedgerApi {

    /**
     * 分页列表查询
     */
    @GetMapping(value = "/list")
    Result<IPage<CostPurchaseOrderLedgerDTO>> queryPageList(CostPurchaseOrderLedgerDTO queryVO,
                                                           @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                           @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize);

    /**
     * 通过Excel导入数据
     */
    @PostMapping(value = "/importExcel")
    Result<ExcelImportResult> importExcel(HttpServletRequest request, HttpServletResponse response);

    /**
     * 导出Excel模板
     */
    @GetMapping(value = "/exportTemplate")
    void exportTemplate(HttpServletResponse response);
}
