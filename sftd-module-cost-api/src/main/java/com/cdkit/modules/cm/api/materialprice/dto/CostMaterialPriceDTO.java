package com.cdkit.modules.cm.api.materialprice.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.cdkit.common.aspect.annotation.Dict;
import com.cdkitframework.poi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 材料单价管理DTO
 * @Author: sunhzh
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Schema(description="材料单价管理DTO")
@Data
public class CostMaterialPriceDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @TableId(type = IdType.ASSIGN_ID)
    @Schema(description = "主键ID")
    private String id;

    /**物料名称*/
    @Excel(name = "物料名称", width = 15)
    @Schema(description = "物料名称")
    private String materialName;

    /**物料编码*/
    @Excel(name = "物料编码", width = 15)
    @Schema(description = "物料编码")
    private String materialCode;

    /**状态: pending_submit-待提交, not_effective-未生效, in_effect-生效中, expired-已失效*/
    @Excel(name = "状态", width = 15, dicCode = "cost_material_price_status")
    @Dict(dicCode = "cost_material_price_status")
    @Schema(description = "状态: pending_submit-待提交, not_effective-未生效, in_effect-生效中, expired-已失效")
    private String materialPriceStatus;

    /**含税单价*/
    @Excel(name = "含税单价", width = 15)
    @Schema(description = "含税单价")
    private BigDecimal taxUnitPrice;

    /**不含税单价*/
    @Excel(name = "不含税单价", width = 15)
    @Schema(description = "不含税单价")
    private BigDecimal nonTaxUnitPrice;

    /**类型: fixed-固定, float-浮动*/
    @Excel(name = "类型", width = 15, dicCode = "cost_price_type")
    @Dict(dicCode = "cost_price_type")
    @Schema(description = "类型: fixed-固定, float-浮动")
    private String priceType;

    /**上浮比例*/
    @Excel(name = "上浮比例", width = 15)
    @Schema(description = "上浮比例")
    private BigDecimal upRate;

    /**来源*/
    @Excel(name = "来源", width = 15)
    @Schema(description = "来源")
    private String source;

    /**生效日期*/
    @Excel(name = "生效日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "生效日期")
    private Date effectiveDate;

    /**失效日期*/
    @Excel(name = "失效日期", width = 15, format = "yyyy-MM-dd")
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "失效日期")
    private Date expirationDate;

    /**税率(%)*/
    @Excel(name = "税率(%)", width = 15)
    @Schema(description = "税率(%)")
    private BigDecimal taxRate;

    /**创建时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private Date createTime;

    /**创建人*/
    @Schema(description = "创建人")
    @Dict(dictTable = "sys_user", dicText = "realname", dicCode = "username", ds = "master")
    private String createBy;

    /**更新时间*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @Schema(description = "更新时间")
    private Date updateTime;

    /**更新人*/
    @Schema(description = "更新人")
    private String updateBy;

    /**租户ID*/
    @Excel(name = "租户ID", width = 15)
    @Schema(description = "租户ID")
    private Integer tenantId;

    /**删除标识 0:未删除 1:删除*/
    @Excel(name = "删除标识 0:未删除 1:删除", width = 15)
    @Schema(description = "删除标识 0:未删除 1:删除")
    private Integer delFlag;
}
