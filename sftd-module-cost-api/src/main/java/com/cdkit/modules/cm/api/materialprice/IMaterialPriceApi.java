package com.cdkit.modules.cm.api.materialprice;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cdkit.common.api.vo.Result;
import com.cdkit.modules.cm.api.materialprice.dto.CostMaterialPriceAddRequest;
import com.cdkit.modules.cm.api.materialprice.dto.CostMaterialPriceDTO;
import com.cdkit.modules.cm.api.materialprice.dto.CostMaterialPriceEditRequest;
import com.cdkit.modules.cm.domain.gateway.material.entity.MaterialDetailDTO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

/**
 * 材料单价管理API
 * <AUTHOR>
 * @date 2025/07/17
 */
public interface IMaterialPriceApi {

    @GetMapping(value = "/list")
    Result<IPage<CostMaterialPriceDTO>> queryPageList(
            @RequestParam(name="materialName", required=false) String materialName,
            @RequestParam(name="materialCode", required=false) String materialCode,
            @RequestParam(name="materialPriceStatus", required=false) String materialPriceStatus,
            @RequestParam(name="priceType", required=false) String priceType,
            @RequestParam(name="source", required=false) String source,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize);

    @PostMapping(value = "/add")
    Result<String> add(@Valid @RequestBody CostMaterialPriceAddRequest request);

    @PutMapping(value = "/edit")
    Result<String> edit(@Valid @RequestBody CostMaterialPriceEditRequest request);

    @DeleteMapping(value = "/delete")
    Result<String> delete(@RequestParam(name="id", required=true) String id);

    @DeleteMapping(value = "/deleteBatch")
    Result<String> deleteBatch(@RequestParam(name="ids", required=true) String ids);

    @PostMapping(value = "/submit")
    Result<String> submit(@RequestParam(name="id", required=true) String id);

    @GetMapping(value = "/queryById")
    Result<CostMaterialPriceDTO> queryById(@RequestParam(name="id", required=true) String id);

    @GetMapping(value = "/getLatestPrice")
    Result<CostMaterialPriceDTO> getLatestPrice(@RequestParam(name="materialCode", required=true) String materialCode);

    @GetMapping(value = "/selectMaterial")
    Page<MaterialDetailDTO> selectMaterialByPage(
            @RequestParam(name="materialCode", required=false) String materialCode,
            @RequestParam(name="materialName", required=false) String materialName,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize);

    @RequestMapping(value = "/exportXls")
    ModelAndView exportXls(CostMaterialPriceDTO costMaterialPriceDTO);

    @RequestMapping(value = "/exportXlsTemplate")
    ModelAndView exportXlsTemplate(CostMaterialPriceDTO costMaterialPriceDTO);

    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    Result<?> importExcel(HttpServletRequest request, HttpServletResponse response);
}
