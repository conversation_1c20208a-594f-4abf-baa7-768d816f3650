package com.cdkit.modules.cm.api.materialprice.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.DecimalMin;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Description: 材料单价编辑请求
 * @Author: sunhzh
 * @Date:   2025-07-16
 * @Version: V1.0
 */
@Schema(description="材料单价编辑请求")
@Data
public class CostMaterialPriceEditRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    /**主键ID*/
    @NotBlank(message = "ID不能为空")
    @Schema(description = "主键ID", required = true)
    private String id;

    /**物料名称*/
    @NotBlank(message = "物料名称不能为空")
    @Schema(description = "物料名称", required = true)
    private String materialName;

    /**物料编码*/
    @Schema(description = "物料编码")
    private String materialCode;

    /**含税单价*/
    @Schema(description = "含税单价")
    @DecimalMin(value = "0", message = "含税单价不能小于0")
    private BigDecimal taxUnitPrice;

    /**不含税单价*/
    @Schema(description = "不含税单价")
    @DecimalMin(value = "0", message = "不含税单价不能小于0")
    private BigDecimal nonTaxUnitPrice;

    /**类型: fixed-固定, float-浮动*/
    @Schema(description = "类型: fixed-固定, float-浮动")
    private String priceType;

    /**上浮比例*/
    @Schema(description = "上浮比例")
    private BigDecimal upRate;

    /**来源*/
    @Schema(description = "来源")
    private String source;

    /**生效日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "生效日期")
    private Date effectiveDate;

    /**失效日期*/
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern="yyyy-MM-dd")
    @Schema(description = "失效日期")
    private Date expirationDate;

    /**税率(%)*/
    @Schema(description = "税率(%)")
    @DecimalMin(value = "0", message = "税率不能小于0")
    private BigDecimal taxRate;
}
